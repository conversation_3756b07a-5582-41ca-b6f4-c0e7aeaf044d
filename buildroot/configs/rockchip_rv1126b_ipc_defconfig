BR2_aarch64=y
BR2_GCC_VERSION_12_X=y
BR2_TOOLCHAIN_BUILDROOT_CXX=y
BR2_TOOLCHAIN_BUILDROOT_FORTRAN=y
BR2_GCC_ENABLE_OPENMP=y
BR2_GCC_ENABLE_GRAPHITE=y
BR2_PACKAGE_GCC_TARGET=y
BR2_PRIMARY_SITE="https://sources.buildroot.net"
BR2_BACKUP_SITE=""
BR2_GNU_MIRROR="https://mirrors.ustc.edu.cn/gnu"
BR2_FORCE_HOST_BUILD=y
BR2_TARGET_GENERIC_HOSTNAME="rv1126b"
BR2_TARGET_GENERIC_ISSUE="Welcome to RV1126B Buildroot"
BR2_ROOTFS_DEVICE_CREATION_DYNAMIC_EUDEV=y
BR2_ROOTFS_MERGED_USR=y
BR2_TARGET_GENERIC_ROOT_PASSWD="admin@vispect1209"
BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay-ipc/"
BR2_ROOTFS_POST_BUILD_SCRIPT="board/rockchip/common/post-build.sh"
BR2_LINUX_KERNEL_CUSTOM_LOCAL=y
BR2_LINUX_KERNEL_CUSTOM_LOCAL_LOCATION="$(TOPDIR)/../kernel"
BR2_PACKAGE_ROCKCHIP=y
BR2_PACKAGE_RV1126B=y
BR2_PACKAGE_CAMERA_ENGINE=y
BR2_PACKAGE_IPC_DRV_KO=y
BR2_PACKAGE_RKMEDIA_MPP=y
BR2_PACKAGE_RKMEDIA_ALSA=y
BR2_PACKAGE_RKMEDIA_ALSA_PLAYBACK=y
BR2_PACKAGE_RKMEDIA_ALSA_CAPTURE=y
BR2_PACKAGE_RKMEDIA_AUDIO_ALGORITHM=y
BR2_PACKAGE_RKMEDIA_AUDIO_CODEC=y
BR2_PACKAGE_RKMEDIA_AUDIO_ENCODER=y
BR2_PACKAGE_RKMEDIA_AUDIO_DECODER=y
BR2_PACKAGE_RKMEDIA_RKRGA=y
BR2_PACKAGE_RKMEDIA_RKNN=y
BR2_PACKAGE_RKMEDIA_MOVE_DETECTION=y
BR2_PACKAGE_RKMEDIA_OCCLUSION_DETECTION=y
BR2_PACKAGE_RKMEDIA_EXAMPLES=y
BR2_PACKAGE_RKMEDIA_LIVE555=y
BR2_PACKAGE_RKMEDIA_RTSP_SERVER=y
BR2_PACKAGE_RKMEDIA_RTSP_SERVER_H264=y
BR2_PACKAGE_RKMEDIA_RTSP_SERVER_H265=y
BR2_PACKAGE_RKSCRIPT=y
BR2_PACKAGE_RKSCRIPT_BOOTANIM=y
BR2_PACKAGE_RKWIFIBT=y
BR2_PACKAGE_RKWIFIBT_APP=y
BR2_PACKAGE_ROCKCHIP_ALSA_CONFIG=y
BR2_PACKAGE_ROCKCHIP_MPP=y
BR2_PACKAGE_ROCKCHIP_MPP_ALLOCATOR_DRM=y
BR2_PACKAGE_ROCKCHIP_MPP_TESTS=y
BR2_PREFER_ROCKCHIP_RGA=y
BR2_PACKAGE_ROCKCHIP_TEST=y
BR2_PACKAGE_IPCWEB_BACKEND=y
BR2_PACKAGE_IPCWEB_BACKEND_USE_RKIPC=y
BR2_PACKAGE_IVA=y
BR2_PACKAGE_IVA_RV1126B=y
BR2_PACKAGE_RKFSMK=y
BR2_PACKAGE_RKIPC=y
BR2_PACKAGE_RKIPC_RV1126B=y
BR2_PACKAGE_SAMPLES=y
BR2_PACKAGE_SAMPLES_RV1126B=y
BR2_PACKAGE_BUSYBOX_CONFIG_FRAGMENT_FILES="board/rockchip/common/base/busybox.fragment"
BR2_PACKAGE_BUSYBOX_SHOW_OTHERS=y
BR2_PACKAGE_BLUEZ_ALSA_HCITOP=y
BR2_PACKAGE_GDB=y
BR2_PACKAGE_GDB_DEBUGGER=y
BR2_PACKAGE_LIBTOOL=y
BR2_PACKAGE_E2FSPROGS=y
BR2_PACKAGE_E2FSPROGS_RESIZE2FS=y
BR2_PACKAGE_INPUT_EVENT_DAEMON=y
BR2_PACKAGE_PM_UTILS=y
BR2_PACKAGE_USBMOUNT=y
BR2_PACKAGE_LIBSNDFILE=y
BR2_PACKAGE_MP4V2=y
BR2_PACKAGE_LIBJPEG=y
BR2_PACKAGE_OPENCV3=y
BR2_PACKAGE_OPENCV3_LIB_SHAPE=y
BR2_PACKAGE_OPENCV3_LIB_STITCHING=y
BR2_PACKAGE_OPENCV3_LIB_SUPERRES=y
BR2_PACKAGE_OPENCV3_LIB_TS=y
BR2_PACKAGE_OPENCV3_LIB_VIDEOSTAB=y
BR2_PACKAGE_OPENCV3_BUILD_TESTS=y
BR2_PACKAGE_OPENCV3_BUILD_PERF_TESTS=y
BR2_PACKAGE_OPENCV3_WITH_FFMPEG=y
BR2_PACKAGE_OPENCV3_WITH_GSTREAMER1=y
BR2_PACKAGE_OPENCV3_WITH_JASPER=y
BR2_PACKAGE_OPENCV3_WITH_JPEG=y
BR2_PACKAGE_OPENCV3_WITH_PNG=y
BR2_PACKAGE_OPENCV3_WITH_PROTOBUF=y
BR2_PACKAGE_OPENCV3_WITH_TIFF=y
BR2_PACKAGE_OPENCV3_WITH_V4L=y
BR2_PACKAGE_OPENCV3_WITH_WEBP=y
BR2_PACKAGE_OPENCV3_INSTALL_DATA=y
BR2_PACKAGE_LIBGPIOD=y
BR2_PACKAGE_LIBGPIOD_TOOLS=y
BR2_PACKAGE_LIBIIO=y
BR2_PACKAGE_LIBIIO_TESTS=y
BR2_PACKAGE_CJSON=y
BR2_PACKAGE_JSONCPP=y
BR2_PACKAGE_YAML_CPP=y
BR2_PACKAGE_LIBCURL=y
BR2_PACKAGE_LIBPCAP=y
BR2_PACKAGE_BOOST=y
BR2_PACKAGE_BOOST_CONTRACT=y
BR2_PACKAGE_BOOST_COROUTINE=y
BR2_PACKAGE_BOOST_EXCEPTION=y
BR2_PACKAGE_BOOST_FIBER=y
BR2_PACKAGE_BOOST_GRAPH=y
BR2_PACKAGE_BOOST_GRAPH_PARALLEL=y
BR2_PACKAGE_BOOST_IOSTREAMS=y
BR2_PACKAGE_BOOST_JSON=y
BR2_PACKAGE_BOOST_LOCALE=y
BR2_PACKAGE_BOOST_LOG=y
BR2_PACKAGE_BOOST_MATH=y
BR2_PACKAGE_BOOST_MPI=y
BR2_PACKAGE_BOOST_NOWIDE=y
BR2_PACKAGE_BOOST_PROGRAM_OPTIONS=y
BR2_PACKAGE_BOOST_RANDOM=y
BR2_PACKAGE_BOOST_SERIALIZATION=y
BR2_PACKAGE_BOOST_STACKTRACE=y
BR2_PACKAGE_BOOST_TEST=y
BR2_PACKAGE_BOOST_TYPE_ERASURE=y
BR2_PACKAGE_BOOST_URL=y
BR2_PACKAGE_BOOST_WAVE=y
BR2_PACKAGE_POCO=y
BR2_PACKAGE_POCO_DATA_SQLITE=y
BR2_PACKAGE_POCO_NETSSL_OPENSSL=y
BR2_PACKAGE_POCO_ZIP=y
BR2_PACKAGE_AVAHI=y
BR2_PACKAGE_AVAHI_DAEMON=y
BR2_PACKAGE_AVAHI_LIBDNSSD_COMPATIBILITY=y
BR2_PACKAGE_CAN_UTILS=y
BR2_PACKAGE_CHRONY=y
BR2_PACKAGE_DNSMASQ=y
BR2_PACKAGE_DROPBEAR=y
# BR2_PACKAGE_DROPBEAR_CLIENT is not set
BR2_PACKAGE_ETHTOOL=y
BR2_PACKAGE_GESFTPSERVER=y
BR2_PACKAGE_HOSTAPD=y
BR2_PACKAGE_IPROUTE2=y
BR2_PACKAGE_NET_TOOLS=y
BR2_PACKAGE_OPENSSH=y
BR2_PACKAGE_WPA_SUPPLICANT_AP_SUPPORT=y
BR2_PACKAGE_WPA_SUPPLICANT_AUTOSCAN=y
BR2_PACKAGE_WPA_SUPPLICANT_EAP=y
BR2_PACKAGE_WPA_SUPPLICANT_PASSPHRASE=y
BR2_PACKAGE_ANDROID_ADBD=y
BR2_PACKAGE_HTOP=y
BR2_PACKAGE_UTIL_LINUX_MOUNT=y
BR2_PACKAGE_LESS=y
BR2_TARGET_ROOTFS_CPIO=y
BR2_TARGET_ROOTFS_CPIO_GZIP=y
BR2_TARGET_ROOTFS_EXT2=y
BR2_TARGET_ROOTFS_EXT2_4=y
BR2_TARGET_ROOTFS_EXT2_SIZE_AUTO=y
BR2_TARGET_ROOTFS_SQUASHFS=y
BR2_PACKAGE_HOST_ENVIRONMENT_SETUP=y
BR2_PACKAGE_HOST_NTFS_3G=y
BR2_PACKAGE_HOST_PYTHON3=y
BR2_PACKAGE_HOST_PYTHON3_SSL=y
BR2_arm=y