SHELL = /bin/bash

ROCKIT_CURRENT_DIR := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))

SDK_ROOT_DIR := $(ROCKIT_CURRENT_DIR)/../../../../../../../

PKG_NAME_ROCKIT_KO := rockit-ko

PKG_TARGET:=rockit-build

all: $(PKG_TARGET)

rockit-build:
	mkdir -p $(ROCKIT_CURRENT_DIR)/release_rockit-ko_rv1126b_arm64
	$(MAKE) -C $(ROCKIT_CURRENT_DIR) -f Makefile_module \
		CROSS_COMPILE=$(SDK_ROOT_DIR)/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu- \
		ARCH=arm64 \
		KERNEL_DIR=$(SDK_ROOT_DIR)/kernel-6.1
	cp $(ROCKIT_CURRENT_DIR)/rockit.ko $(ROCKIT_CURRENT_DIR)/release_rockit-ko_rv1126b_arm64/ || echo "rockit.ko build failed"
	echo "build rockit-ko"
