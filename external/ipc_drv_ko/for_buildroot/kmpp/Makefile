SHELL = /bin/bash

KMPP_CURRENT_DIR := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))

SDK_ROOT_DIR := $(KMPP_CURRENT_DIR)/../../../../../../../

PKG_NAME_KMPP := kmpp

PKG_TARGET:=kmpp-build

all: $(PKG_TARGET)

kmpp-build:
	mkdir -p $(KMPP_CURRENT_DIR)/release_kmpp_rv1126b_arm64
	$(MAKE) -C $(KMPP_CURRENT_DIR) -f Makefile_module \
		CROSS_COMPILE=$(SDK_ROOT_DIR)/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu- \
		ARCH=arm64 \
		KERNEL_DIR=$(SDK_ROOT_DIR)/kernel-6.1
	cp $(KMPP_CURRENT_DIR)/kmpp.ko $(KMPP_CURRENT_DIR)/release_kmpp_rv1126b_arm64/ || echo "kmpp.ko build failed"
	echo "build kmpp"
